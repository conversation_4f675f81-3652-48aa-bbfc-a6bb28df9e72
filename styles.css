/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2rem;
}

.exam-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.exam-info span {
    background: #3498db;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.screen {
    display: none;
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.screen.active {
    display: flex;
    flex-direction: column;
}

/* Start Screen */
.start-container {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.start-container h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 2.5rem;
}

.start-container p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #555;
}

.mode-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.mode-card {
    background: #f8f9fa;
    border: 3px solid #e9ecef;
    border-radius: 10px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.mode-card.selected {
    border-color: #27ae60;
    background: #e8f5e8;
}

.mode-card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.mode-card p {
    color: #666;
    margin-bottom: 8px;
    text-align: left;
    font-size: 1rem;
}

.file-upload {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.file-upload label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2c3e50;
}

.file-upload input[type="file"] {
    width: 100%;
    padding: 10px;
    border: 2px dashed #ccc;
    border-radius: 5px;
    background: white;
}

.file-info {
    font-size: 0.9rem;
    color: #666;
    margin-top: 8px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #7f8c8d;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #229954;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: #e67e22;
}

/* Question Screen */
.question-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.question-header {
    margin-bottom: 30px;
}

.question-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #27ae60);
    transition: width 0.3s ease;
    width: 0%;
}

.question-content {
    flex: 1;
    margin-bottom: 30px;
}

#question-text {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 25px;
    line-height: 1.6;
}

.options-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.option:hover {
    border-color: #3498db;
    background: #e3f2fd;
}

.option.selected {
    border-color: #27ae60;
    background: #e8f5e8;
}

.option.correct {
    border-color: #27ae60;
    background: #d4edda;
}

.option.incorrect {
    border-color: #e74c3c;
    background: #f8d7da;
}

.option-letter {
    font-weight: bold;
    color: #2c3e50;
    min-width: 20px;
}

.option-text {
    flex: 1;
    color: #333;
}

/* Feedback Section */
.feedback-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.feedback-result {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.feedback-result.correct {
    color: #27ae60;
}

.feedback-result.incorrect {
    color: #e74c3c;
}

.feedback-explanation {
    color: #555;
    line-height: 1.6;
}

.question-navigation {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

/* Results Screen */
.results-container {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.results-container h2 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2.5rem;
}

.score-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.score-circle {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: conic-gradient(#27ae60 0deg, #27ae60 0deg, #e9ecef 0deg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 160px;
    height: 160px;
    background: white;
    border-radius: 50%;
    z-index: 1;
}

.score-percentage {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    z-index: 2;
}

.score-fraction {
    font-size: 1.2rem;
    color: #666;
    z-index: 2;
}

.score-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 8px;
    min-width: 200px;
}

.score-item .label {
    font-weight: 600;
    color: #2c3e50;
}

.score-item .value {
    font-size: 1.2rem;
    font-weight: bold;
}

.results-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.review-section {
    text-align: left;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
}

.review-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.review-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.review-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #e9ecef;
}

.review-item.correct {
    border-left-color: #27ae60;
}

.review-item.incorrect {
    border-left-color: #e74c3c;
}

.review-item.unanswered {
    border-left-color: #f39c12;
}

.review-question {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.review-answer {
    font-size: 0.9rem;
    color: #666;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    text-align: center;
}

.modal-content h3 {
    color: #e74c3c;
    margin-bottom: 15px;
}

.modal-content p {
    margin-bottom: 20px;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .exam-info {
        justify-content: center;
    }

    .start-container h2 {
        font-size: 2rem;
    }

    .mode-selection {
        grid-template-columns: 1fr;
    }

    .question-navigation {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 200px;
    }

    .score-summary {
        flex-direction: column;
        gap: 20px;
    }

    .score-circle {
        width: 150px;
        height: 150px;
    }

    .score-circle::before {
        width: 120px;
        height: 120px;
    }

    .score-percentage {
        font-size: 2rem;
    }

    .results-actions {
        flex-direction: column;
        align-items: center;
    }
}
