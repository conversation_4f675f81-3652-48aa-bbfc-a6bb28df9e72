// Exam Practice Application
class ExamApp {
    constructor() {
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.examMode = 'practice'; // 'practice' or 'exam'
        this.isExamFinished = false;
        
        this.initializeApp();
        this.loadDefaultQuestions();
    }

    initializeApp() {
        this.bindEvents();
        this.showScreen('start-screen');
    }

    bindEvents() {
        // Mode selection
        document.querySelectorAll('.mode-card').forEach(card => {
            card.addEventListener('click', () => this.selectMode(card));
        });

        // File upload
        document.getElementById('exam-file').addEventListener('change', (e) => {
            this.handleFileUpload(e);
        });

        // Start exam
        document.getElementById('start-exam').addEventListener('click', () => {
            this.startExam();
        });

        // Question navigation
        document.getElementById('prev-question').addEventListener('click', () => {
            this.navigateQuestion(-1);
        });

        document.getElementById('next-question').addEventListener('click', () => {
            this.navigateQuestion(1);
        });

        document.getElementById('submit-answer').addEventListener('click', () => {
            this.submitAnswer();
        });

        document.getElementById('finish-exam').addEventListener('click', () => {
            this.finishExam();
        });

        // Results actions
        document.getElementById('review-answers').addEventListener('click', () => {
            this.showReview();
        });

        document.getElementById('restart-exam').addEventListener('click', () => {
            this.restartExam();
        });

        // Modal close
        document.getElementById('close-error').addEventListener('click', () => {
            this.hideModal('error-modal');
        });

        // Option selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.option')) {
                this.selectOption(e.target.closest('.option'));
            }
        });
    }

    selectMode(selectedCard) {
        document.querySelectorAll('.mode-card').forEach(card => {
            card.classList.remove('selected');
        });
        selectedCard.classList.add('selected');
        
        this.examMode = selectedCard.dataset.mode;
        document.getElementById('start-exam').disabled = false;
        
        // Update mode display
        const modeDisplay = this.examMode === 'practice' ? 'Practice Mode' : 'Real Exam Mode';
        document.getElementById('exam-mode-display').textContent = modeDisplay;
    }

    async handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        this.showLoading(true);
        
        try {
            const text = await this.readFile(file);
            const questions = this.parseQuestions(text);
            
            if (questions.length === 0) {
                throw new Error('No valid questions found in the file');
            }
            
            this.questions = questions;
            this.showMessage('success', `Loaded ${questions.length} questions from file`);
        } catch (error) {
            this.showError('Error loading file: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    readFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsText(file);
        });
    }

    parseQuestions(text) {
        const questions = [];
        const lines = text.split('\n');
        let currentQuestion = null;
        let questionText = '';
        let options = [];
        let correctAnswer = '';

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Question start
            if (line.match(/^Question #?\d+/i)) {
                // Save previous question if exists
                if (currentQuestion && questionText && options.length > 0) {
                    questions.push({
                        id: currentQuestion,
                        text: questionText.trim(),
                        options: [...options],
                        correctAnswer: correctAnswer,
                        explanation: ''
                    });
                }
                
                // Start new question
                currentQuestion = line;
                questionText = '';
                options = [];
                correctAnswer = '';
                continue;
            }
            
            // Correct answer
            if (line.match(/^Correct Answer:/i)) {
                correctAnswer = line.replace(/^Correct Answer:\s*/i, '').trim();
                continue;
            }
            
            // Options (A., B., C., D., etc.)
            if (line.match(/^[A-Z]\./)) {
                options.push(line);
                continue;
            }
            
            // Question text (everything else that's not empty)
            if (line && !line.match(/^Question #?\d+/i) && !line.match(/^Correct Answer:/i)) {
                questionText += (questionText ? ' ' : '') + line;
            }
        }
        
        // Don't forget the last question
        if (currentQuestion && questionText && options.length > 0) {
            questions.push({
                id: currentQuestion,
                text: questionText.trim(),
                options: [...options],
                correctAnswer: correctAnswer,
                explanation: ''
            });
        }

        return questions;
    }

    loadDefaultQuestions() {
        // This will be populated with the VCF questions from the text file
        // For now, we'll load them via fetch or embed them directly
        this.loadVCFQuestions();
    }

    async loadVCFQuestions() {
        try {
            this.showLoading(true);

            // Try to load from the text file
            try {
                const response = await fetch('vdf administrator.txt');
                if (response.ok) {
                    const text = await response.text();
                    this.questions = this.parseQuestions(text);

                    if (this.questions.length > 0) {
                        console.log(`Loaded ${this.questions.length} questions from file`);
                        return;
                    }
                }
            } catch (fetchError) {
                console.log('Could not fetch external file, using embedded questions');
            }

            // Fallback to embedded questions
            this.loadEmbeddedQuestions();

        } catch (error) {
            console.error('Error loading questions:', error);
            this.loadEmbeddedQuestions();
        } finally {
            this.showLoading(false);
        }
    }

    loadEmbeddedQuestions() {
        // Use questions from the external questions.js file if available
        if (typeof window !== 'undefined' && window.examQuestions) {
            this.questions = window.examQuestions;
            console.log(`Loaded ${this.questions.length} questions from questions.js`);
            return;
        }

        // Fallback embedded questions if external file not available
        this.questions = [
            {
                id: "Question #1",
                text: "What is a symptom of an issue with vSphere HA?",
                options: [
                    "A. Snapshots cannot be created or consolidated.",
                    "B. VMs are not restarted after a host failure.",
                    "C. VMs are not being migrated using vMotion.",
                    "D. Hosts frequently disconnect from vCenter Server."
                ],
                correctAnswer: "B",
                explanation: "vSphere HA is responsible for restarting VMs after host failures."
            },
            {
                id: "Question #2",
                text: "Which prerequisite task must be completed before deploying VMware Cloud Foundation (VCF) using VMware Cloud Builder?",
                options: [
                    "A. Ensure that all ESXi hosts are connected to a single vCenter Server.",
                    "B. Verify network configurations and DNS settings for all components.",
                    "C. Verify configurations on Virtual Distributed Switch on the ESXi hosts that will be used for the deployment.",
                    "D. Ensure HCX is configured for workload migration."
                ],
                correctAnswer: "B",
                explanation: "Network configurations and DNS settings are critical prerequisites for VCF deployment."
            }
        ];

        console.log(`Loaded ${this.questions.length} fallback embedded questions`);
    }

    startExam() {
        if (this.questions.length === 0) {
            this.showError('No questions available. Please load a question file.');
            return;
        }

        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.isExamFinished = false;
        
        this.updateProgress();
        this.showScreen('question-screen');
        this.displayQuestion();
    }

    displayQuestion() {
        const question = this.questions[this.currentQuestionIndex];
        if (!question) return;

        // Update question text
        document.getElementById('question-text').textContent = question.text;
        
        // Update question number
        document.getElementById('current-question').textContent = this.currentQuestionIndex + 1;
        document.getElementById('total-questions').textContent = this.questions.length;
        
        // Create options
        const optionsContainer = document.getElementById('question-options');
        optionsContainer.innerHTML = '';
        
        question.options.forEach((option, index) => {
            const optionElement = this.createOptionElement(option, index);
            optionsContainer.appendChild(optionElement);
        });
        
        // Update navigation buttons
        this.updateNavigationButtons();
        
        // Hide feedback in exam mode
        if (this.examMode === 'exam') {
            document.getElementById('feedback-section').classList.add('hidden');
        }
        
        // Show previous answer if exists
        const userAnswer = this.userAnswers[this.currentQuestionIndex];
        if (userAnswer !== undefined) {
            this.selectOptionByIndex(userAnswer);
            
            // Show feedback in practice mode
            if (this.examMode === 'practice') {
                this.showFeedback();
            }
        }
        
        this.updateProgress();
    }

    createOptionElement(optionText, index) {
        const optionDiv = document.createElement('div');
        optionDiv.className = 'option';
        optionDiv.dataset.index = index;
        
        const letter = optionText.charAt(0);
        const text = optionText.substring(2).trim();
        
        optionDiv.innerHTML = `
            <span class="option-letter">${letter}</span>
            <span class="option-text">${text}</span>
        `;
        
        return optionDiv;
    }

    selectOption(optionElement) {
        const question = this.questions[this.currentQuestionIndex];
        const answerIndex = parseInt(optionElement.dataset.index);

        if (question.multipleChoice) {
            // Handle multiple choice questions
            optionElement.classList.toggle('selected');

            // Get all selected options
            const selectedOptions = Array.from(document.querySelectorAll('.option.selected'))
                .map(opt => parseInt(opt.dataset.index));

            this.userAnswers[this.currentQuestionIndex] = selectedOptions;
        } else {
            // Handle single choice questions
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });

            optionElement.classList.add('selected');
            this.userAnswers[this.currentQuestionIndex] = answerIndex;
        }

        // Show feedback immediately in practice mode
        if (this.examMode === 'practice') {
            this.showFeedback();
        }

        // Update score display
        this.updateScoreDisplay();
    }

    selectOptionByIndex(answerData) {
        const question = this.questions[this.currentQuestionIndex];
        const options = document.querySelectorAll('.option');

        if (question.multipleChoice && Array.isArray(answerData)) {
            // Handle multiple choice - select multiple options
            answerData.forEach(index => {
                if (options[index]) {
                    options[index].classList.add('selected');
                }
            });
        } else if (!question.multipleChoice && typeof answerData === 'number') {
            // Handle single choice - select one option
            if (options[answerData]) {
                options[answerData].classList.add('selected');
            }
        }
    }

    showFeedback() {
        const question = this.questions[this.currentQuestionIndex];
        const userAnswer = this.userAnswers[this.currentQuestionIndex];

        if (userAnswer === undefined) return;

        let isCorrect = false;
        let userAnswerLetters = '';

        if (question.multipleChoice) {
            // Handle multiple choice questions
            if (Array.isArray(userAnswer)) {
                userAnswerLetters = userAnswer.map(index => question.options[index].charAt(0)).sort().join('');
                isCorrect = userAnswerLetters === question.correctAnswer;
            }
        } else {
            // Handle single choice questions
            userAnswerLetters = question.options[userAnswer].charAt(0);
            isCorrect = userAnswerLetters === question.correctAnswer;
        }

        // Update option styles
        document.querySelectorAll('.option').forEach((option, index) => {
            const optionLetter = option.querySelector('.option-letter').textContent;

            // Mark correct answers
            if (question.correctAnswer.includes(optionLetter)) {
                option.classList.add('correct');
            }

            // Mark incorrect user selections
            if (question.multipleChoice) {
                if (Array.isArray(userAnswer) && userAnswer.includes(index) && !question.correctAnswer.includes(optionLetter)) {
                    option.classList.add('incorrect');
                }
            } else {
                if (index === userAnswer && !isCorrect) {
                    option.classList.add('incorrect');
                }
            }
        });

        // Show feedback section
        const feedbackSection = document.getElementById('feedback-section');
        const feedbackResult = document.getElementById('feedback-result');
        const feedbackExplanation = document.getElementById('feedback-explanation');

        feedbackResult.textContent = isCorrect ? 'Correct!' : `Incorrect. The correct answer is ${question.correctAnswer}.`;
        feedbackResult.className = `feedback-result ${isCorrect ? 'correct' : 'incorrect'}`;

        if (question.explanation) {
            feedbackExplanation.textContent = question.explanation;
            feedbackExplanation.style.display = 'block';
        } else {
            feedbackExplanation.style.display = 'none';
        }

        feedbackSection.classList.remove('hidden');
    }

    submitAnswer() {
        const userAnswer = this.userAnswers[this.currentQuestionIndex];
        const question = this.questions[this.currentQuestionIndex];

        // Check if answer is provided
        if (userAnswer === undefined ||
            (question.multipleChoice && Array.isArray(userAnswer) && userAnswer.length === 0)) {
            this.showError('Please select an answer before submitting.');
            return;
        }

        if (this.examMode === 'practice') {
            this.showFeedback();
        }

        // Move to next question or finish
        if (this.currentQuestionIndex < this.questions.length - 1) {
            this.navigateQuestion(1);
        } else {
            this.finishExam();
        }
    }

    navigateQuestion(direction) {
        const newIndex = this.currentQuestionIndex + direction;

        if (newIndex >= 0 && newIndex < this.questions.length) {
            this.currentQuestionIndex = newIndex;
            this.displayQuestion();
        }
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prev-question');
        const nextBtn = document.getElementById('next-question');
        const submitBtn = document.getElementById('submit-answer');
        const finishBtn = document.getElementById('finish-exam');

        // Previous button
        prevBtn.disabled = this.currentQuestionIndex === 0;

        // Next/Submit/Finish buttons
        const isLastQuestion = this.currentQuestionIndex === this.questions.length - 1;
        const userAnswer = this.userAnswers[this.currentQuestionIndex];
        const question = this.questions[this.currentQuestionIndex];

        // Check if answer is provided
        let hasAnswer = false;
        if (userAnswer !== undefined) {
            if (question.multipleChoice) {
                hasAnswer = Array.isArray(userAnswer) && userAnswer.length > 0;
            } else {
                hasAnswer = true;
            }
        }

        if (this.examMode === 'practice') {
            nextBtn.style.display = isLastQuestion ? 'none' : 'inline-block';
            submitBtn.style.display = 'none';
            finishBtn.style.display = isLastQuestion ? 'inline-block' : 'none';
        } else {
            nextBtn.style.display = (hasAnswer && !isLastQuestion) ? 'inline-block' : 'none';
            submitBtn.style.display = hasAnswer ? 'none' : 'inline-block';
            finishBtn.style.display = (hasAnswer && isLastQuestion) ? 'inline-block' : 'none';
        }
    }

    updateProgress() {
        const progress = ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
        document.getElementById('progress-fill').style.width = `${progress}%`;

        // Update header progress
        document.getElementById('progress-display').textContent =
            `Question ${this.currentQuestionIndex + 1} of ${this.questions.length}`;
    }

    updateScoreDisplay() {
        const answeredCount = Object.keys(this.userAnswers).length;
        const correctCount = this.calculateCorrectAnswers();

        document.getElementById('score-display').textContent =
            `Score: ${correctCount}/${answeredCount}`;
    }

    calculateCorrectAnswers() {
        let correct = 0;

        for (const [questionIndex, userAnswer] of Object.entries(this.userAnswers)) {
            const question = this.questions[parseInt(questionIndex)];

            if (question.multipleChoice) {
                // Handle multiple choice questions
                if (Array.isArray(userAnswer)) {
                    const userAnswerLetters = userAnswer.map(index => question.options[index].charAt(0)).sort().join('');
                    if (userAnswerLetters === question.correctAnswer) {
                        correct++;
                    }
                }
            } else {
                // Handle single choice questions
                const userAnswerLetter = question.options[userAnswer].charAt(0);
                if (userAnswerLetter === question.correctAnswer) {
                    correct++;
                }
            }
        }

        return correct;
    }

    finishExam() {
        this.isExamFinished = true;
        this.showResults();
    }

    showResults() {
        const totalQuestions = this.questions.length;
        const answeredCount = Object.keys(this.userAnswers).length;
        const correctCount = this.calculateCorrectAnswers();
        const incorrectCount = answeredCount - correctCount;
        const unansweredCount = totalQuestions - answeredCount;
        const percentage = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 100) : 0;

        // Update score circle
        const scoreCircle = document.querySelector('.score-circle');
        const angle = (correctCount / totalQuestions) * 360;
        scoreCircle.style.background = `conic-gradient(#27ae60 0deg, #27ae60 ${angle}deg, #e9ecef ${angle}deg)`;

        // Update score displays
        document.getElementById('score-percentage').textContent = `${percentage}%`;
        document.getElementById('score-fraction').textContent = `${correctCount}/${totalQuestions}`;
        document.getElementById('correct-count').textContent = correctCount;
        document.getElementById('incorrect-count').textContent = incorrectCount;
        document.getElementById('unanswered-count').textContent = unansweredCount;

        this.showScreen('results-screen');
    }

    showReview() {
        const reviewSection = document.getElementById('review-section');
        const reviewList = document.getElementById('review-list');

        reviewList.innerHTML = '';

        this.questions.forEach((question, index) => {
            const reviewItem = this.createReviewItem(question, index);
            reviewList.appendChild(reviewItem);
        });

        reviewSection.classList.remove('hidden');
    }

    createReviewItem(question, index) {
        const userAnswer = this.userAnswers[index];
        const div = document.createElement('div');
        div.className = 'review-item';

        let status = 'unanswered';
        let statusText = 'Not answered';
        let answerText = '';

        if (userAnswer !== undefined) {
            let userAnswerLetters = '';
            let isCorrect = false;

            if (question.multipleChoice) {
                // Handle multiple choice questions
                if (Array.isArray(userAnswer)) {
                    userAnswerLetters = userAnswer.map(index => question.options[index].charAt(0)).sort().join('');
                    isCorrect = userAnswerLetters === question.correctAnswer;
                }
            } else {
                // Handle single choice questions
                userAnswerLetters = question.options[userAnswer].charAt(0);
                isCorrect = userAnswerLetters === question.correctAnswer;
            }

            status = isCorrect ? 'correct' : 'incorrect';
            statusText = isCorrect ? 'Correct' : 'Incorrect';
            answerText = `Your answer: ${userAnswerLetters} | Correct answer: ${question.correctAnswer}`;
        } else {
            answerText = `Correct answer: ${question.correctAnswer}`;
        }

        div.classList.add(status);
        div.innerHTML = `
            <div class="review-question">Question ${index + 1}: ${question.text}</div>
            <div class="review-answer">${statusText} - ${answerText}</div>
        `;

        return div;
    }

    restartExam() {
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.isExamFinished = false;

        // Reset displays
        document.getElementById('score-display').textContent = 'Score: 0/0';
        document.getElementById('review-section').classList.add('hidden');

        this.showScreen('start-screen');
    }

    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }

    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }

    showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').classList.remove('hidden');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }

    showMessage(type, message) {
        // Simple message display - could be enhanced with a toast system
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ExamApp();
});
